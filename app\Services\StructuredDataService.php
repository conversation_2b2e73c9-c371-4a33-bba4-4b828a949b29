<?php

namespace App\Services;

use App\Models\Page;
use App\Models\BlogPost;
use App\Models\BlogCategory;

class StructuredDataService
{
    private function safeSetting($key, $default = null)
    {
        try {
            return function_exists('getSetting') ? getSetting($key, $default) : $default;
        } catch (\Exception $e) {
            return $default;
        }
    }
    public function generateStructuredData($type = 'general', $data = null)
    {
        try {
            if (!$this->safeSetting('structured_data_enabled', true)) {
                return '';
            }

            $schemas = [];

            switch ($type) {
                case 'general':
                    $schemas = $this->getGeneralSchemas();
                    break;
                case 'article':
                    $schemas = $this->getArticleSchema($data);
                    break;
                case 'breadcrumbs':
                    $schemas = $this->getBreadcrumbSchema($data);
                    break;
                case 'page':
                    $schemas = $this->getPageSchema($data);
                    break;
                case 'service':
                    $schemas = $this->getServiceSchema();
                    break;
                case 'software':
                    $schemas = $this->getSoftwareApplicationSchema();
                    break;
                case 'faq':
                    $schemas = $this->getFAQSchema($data);
                    break;
            }

            if (empty($schemas)) {
                return '';
            }

            return $this->renderJsonLd($schemas);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('StructuredDataService Error: ' . $e->getMessage());
            return '';
        }
    }

    private function getGeneralSchemas()
    {
        $schemas = [];

        try {
            if ($this->safeSetting('structured_data_organization', true)) {
                $schemas[] = $this->getOrganizationSchema();
            }

            if ($this->safeSetting('structured_data_website', true)) {
                $schemas[] = $this->getWebsiteSchema();
            }

            if ($this->safeSetting('structured_data_service', true)) {
                $schemas[] = $this->getServiceSchema();
            }

            if ($this->safeSetting('structured_data_software', true)) {
                $schemas[] = $this->getSoftwareApplicationSchema();
            }

            if ($this->safeSetting('structured_data_faq', true)) {
                $faqSchemas = $this->getFAQSchema();
                if (!empty($faqSchemas)) {
                    $schemas = array_merge($schemas, $faqSchemas);
                }
            }

            if ($this->safeSetting('structured_data_localbusiness', false)) {
                $schemas[] = $this->getLocalBusinessSchema();
            }

            if ($this->safeSetting('structured_data_product', true)) {
                $schemas[] = $this->getProductSchema();
            }

            if ($this->safeSetting('structured_data_howto', true)) {
                $howtoSchemas = $this->getHowToSchema();
                if (!empty($howtoSchemas)) {
                    $schemas = array_merge($schemas, $howtoSchemas);
                }
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('General Schemas Error: ' . $e->getMessage());
        }

        return $schemas;
    }

    private function getOrganizationSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
            'url' => $this->safeSetting('organization_url', url('/')),
            'identifier' => url('/'), // Unique identifier
            'foundingDate' => $this->safeSetting('organization_founding_date', date('Y')),
            'knowsAbout' => [
                'Email Privacy',
                'Temporary Email',
                'Spam Protection',
                'Online Privacy',
                'Digital Security'
            ]
        ];

        if ($logo = $this->safeSetting('organization_logo')) {
            $schema['logo'] = [
                '@type' => 'ImageObject',
                'url' => asset($logo),
                'width' => '600',
                'height' => '60'
            ];
        }

        if ($description = $this->safeSetting('organization_description')) {
            $schema['description'] = $description;
        }

        // Add slogan/tagline for better branding
        if ($slogan = $this->safeSetting('organization_slogan')) {
            $schema['slogan'] = $slogan;
        }

        // Contact information
        $contactPoint = [];
        if ($telephone = $this->safeSetting('organization_telephone')) {
            $contactPoint['telephone'] = $telephone;
        }
        if ($email = $this->safeSetting('organization_email')) {
            $contactPoint['email'] = $email;
        }
        if ($contactType = $this->safeSetting('organization_contact_type')) {
            $contactPoint['contactType'] = $contactType;
        }

        if (!empty($contactPoint)) {
            $contactPoint['@type'] = 'ContactPoint';
            $schema['contactPoint'] = $contactPoint;
        }

        // Address
        $address = [];
        if ($street = $this->safeSetting('organization_address_street')) {
            $address['streetAddress'] = $street;
        }
        if ($city = $this->safeSetting('organization_address_city')) {
            $address['addressLocality'] = $city;
        }
        if ($region = $this->safeSetting('organization_address_region')) {
            $address['addressRegion'] = $region;
        }
        if ($postal = $this->safeSetting('organization_address_postal')) {
            $address['postalCode'] = $postal;
        }
        if ($country = $this->safeSetting('organization_address_country')) {
            $address['addressCountry'] = $country;
        }

        if (!empty($address)) {
            $address['@type'] = 'PostalAddress';
            $schema['address'] = $address;
        }

        // Social media
        $socialMedia = [];
        if ($facebook = $this->safeSetting('social_media_facebook')) {
            $socialMedia[] = $facebook;
        }
        if ($twitter = $this->safeSetting('social_media_twitter')) {
            $socialMedia[] = $twitter;
        }
        if ($instagram = $this->safeSetting('social_media_instagram')) {
            $socialMedia[] = $instagram;
        }
        if ($linkedin = $this->safeSetting('social_media_linkedin')) {
            $socialMedia[] = $linkedin;
        }
        if ($youtube = $this->safeSetting('social_media_youtube')) {
            $socialMedia[] = $youtube;
        }

        if (!empty($socialMedia)) {
            $schema['sameAs'] = $socialMedia;
        }

        return $schema;
    }

    private function getWebsiteSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $this->safeSetting('site_name', 'Website'),
            'alternateName' => $this->safeSetting('site_alternate_name', $this->safeSetting('site_name', 'Website')),
            'url' => url('/'),
            'description' => $this->safeSetting('site_description', 'Secure temporary email service for privacy protection'),
            'inLanguage' => function_exists('getCurrentLang') ? getCurrentLang() : 'en',
            'isAccessibleForFree' => true,
            'publisher' => [
                '@type' => 'Organization',
                'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
                'url' => url('/')
            ]
        ];

        // Enhanced search action with multiple search types
        $searchActions = [];

        // Main site search
        $searchActions[] = [
            '@type' => 'SearchAction',
            'target' => [
                '@type' => 'EntryPoint',
                'urlTemplate' => url('/') . '?search={search_term_string}',
                'actionPlatform' => [
                    'http://schema.org/DesktopWebPlatform',
                    'http://schema.org/MobileWebPlatform'
                ]
            ],
            'query-input' => 'required name=search_term_string'
        ];

        // Email domain search (if applicable)
        if ($this->safeSetting('enable_domain_search', true)) {
            $searchActions[] = [
                '@type' => 'SearchAction',
                'target' => [
                    '@type' => 'EntryPoint',
                    'urlTemplate' => url('/') . '?domain={domain_name}',
                    'actionPlatform' => [
                        'http://schema.org/DesktopWebPlatform',
                        'http://schema.org/MobileWebPlatform'
                    ]
                ],
                'query-input' => 'required name=domain_name'
            ];
        }

        $schema['potentialAction'] = $searchActions;

        // Add website categories/topics
        $schema['about'] = [
            [
                '@type' => 'Thing',
                'name' => 'Email Privacy'
            ],
            [
                '@type' => 'Thing',
                'name' => 'Temporary Email Services'
            ],
            [
                '@type' => 'Thing',
                'name' => 'Spam Protection'
            ]
        ];

        return $schema;
    }

    private function getArticleSchema($post)
    {
        if (!$this->safeSetting('structured_data_articles', true) || !$post) {
            return [];
        }

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $post->title,
            'description' => $post->meta_description ?: $post->description,
            'url' => route('posts', $post->slug),
            'datePublished' => $post->created_at->toISOString(),
            'dateModified' => $post->updated_at->toISOString(),
            'inLanguage' => $post->lang ?? (function_exists('getCurrentLang') ? getCurrentLang() : 'en'),
            'wordCount' => str_word_count(strip_tags($post->content ?? '')),
            'author' => [
                '@type' => 'Organization',
                'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
                'url' => url('/')
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
                'url' => url('/')
            ],
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => route('posts', $post->slug)
            ]
        ];

        // Enhanced image handling for rich snippets
        if ($post->image) {
            $schema['image'] = [
                '@type' => 'ImageObject',
                'url' => asset($post->image),
                'width' => '1200',
                'height' => '630'
            ];
        }

        // Publisher logo for rich snippets
        if ($logo = $this->safeSetting('organization_logo')) {
            $schema['publisher']['logo'] = [
                '@type' => 'ImageObject',
                'url' => asset($logo),
                'width' => '600',
                'height' => '60'
            ];
        }

        // Article section and category
        if ($post->category) {
            $schema['articleSection'] = $post->category->name;
            $schema['about'] = [
                '@type' => 'Thing',
                'name' => $post->category->name
            ];
        }

        // Enhanced keywords handling
        if ($post->tags) {
            $tags = explode(',', $post->tags);
            $schema['keywords'] = array_map('trim', $tags);
        }

        // Add reading time estimate
        $wordCount = str_word_count(strip_tags($post->content ?? ''));
        $readingTime = max(1, round($wordCount / 200)); // Average reading speed
        $schema['timeRequired'] = 'PT' . $readingTime . 'M';

        // Add article type based on content
        if (stripos($post->title, 'how to') !== false || stripos($post->content, 'step') !== false) {
            $schema['@type'] = 'HowTo';
        } elseif (stripos($post->title, 'review') !== false) {
            $schema['@type'] = 'Review';
        }

        return [$schema];
    }

    private function getPageSchema($page)
    {
        if (!$page) {
            return [];
        }

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $page->title,
            'description' => $page->meta_description,
            'url' => route('page', $page->slug),
            'datePublished' => $page->created_at->toISOString(),
            'dateModified' => $page->updated_at->toISOString(),
            'isPartOf' => [
                '@type' => 'WebSite',
                'name' => $this->safeSetting('site_name', 'Website'),
                'url' => url('/')
            ]
        ];

        return [$schema];
    }

    private function getBreadcrumbSchema($breadcrumbs)
    {
        if (!$this->safeSetting('structured_data_breadcrumbs', true) || empty($breadcrumbs)) {
            return [];
        }

        $listItems = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $listItems[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }

        return [[
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $listItems
        ]];
    }

    private function renderJsonLd($schemas)
    {
        if (empty($schemas)) {
            return '';
        }

        $output = '';
        foreach ($schemas as $schema) {
            $output .= '<script type="application/ld+json">' . PHP_EOL;
            $output .= json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $output .= '</script>' . PHP_EOL;
        }

        return $output;
    }

    public function getBreadcrumbs($currentPage = null, $category = null)
    {
        $breadcrumbs = [
            [
                'name' => __('Home'),
                'url' => url('/')
            ]
        ];

        if ($category) {
            $breadcrumbs[] = [
                'name' => __('Blog'),
                'url' => route('blog')
            ];
            $breadcrumbs[] = [
                'name' => $category->name,
                'url' => route('category', $category->slug)
            ];
        } elseif (request()->is('*/blog*')) {
            $breadcrumbs[] = [
                'name' => __('Blog'),
                'url' => route('blog')
            ];
        }

        if ($currentPage) {
            $breadcrumbs[] = [
                'name' => $currentPage,
                'url' => url()->current()
            ];
        }

        return $breadcrumbs;
    }

    /**
     * Generate Service schema for temporary email service
     */
    private function getServiceSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Service',
            'name' => $this->safeSetting('service_name', $this->safeSetting('site_name', 'Temporary Email Service')),
            'description' => $this->safeSetting('service_description', 'Secure temporary email service for privacy protection and spam prevention'),
            'url' => url('/'),
            'provider' => [
                '@type' => 'Organization',
                'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
                'url' => url('/')
            ],
            'serviceType' => 'Email Service',
            'category' => 'Internet Service',
            'audience' => [
                '@type' => 'Audience',
                'audienceType' => 'Internet Users'
            ]
        ];

        // Add service features
        $features = [];
        if ($this->safeSetting('service_feature_privacy', true)) {
            $features[] = 'Privacy Protection';
        }
        if ($this->safeSetting('service_feature_temporary', true)) {
            $features[] = 'Temporary Email Addresses';
        }
        if ($this->safeSetting('service_feature_anonymous', true)) {
            $features[] = 'Anonymous Email';
        }
        if ($this->safeSetting('service_feature_spam_protection', true)) {
            $features[] = 'Spam Protection';
        }

        if (!empty($features)) {
            $schema['hasOfferCatalog'] = [
                '@type' => 'OfferCatalog',
                'name' => 'Service Features',
                'itemListElement' => array_map(function($feature) {
                    return [
                        '@type' => 'Offer',
                        'itemOffered' => [
                            '@type' => 'Service',
                            'name' => $feature
                        ]
                    ];
                }, $features)
            ];
        }

        // Add area served if specified
        if ($areaServed = $this->safeSetting('service_area_served')) {
            $schema['areaServed'] = [
                '@type' => 'Place',
                'name' => $areaServed
            ];
        }

        return $schema;
    }

    /**
     * Generate SoftwareApplication schema for the email service
     */
    private function getSoftwareApplicationSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'SoftwareApplication',
            'name' => $this->safeSetting('app_name', $this->safeSetting('site_name', 'Temporary Email App')),
            'description' => $this->safeSetting('app_description', 'Web-based temporary email application for secure and private email communication'),
            'url' => url('/'),
            'applicationCategory' => 'WebApplication',
            'operatingSystem' => 'Web Browser',
            'browserRequirements' => 'Requires JavaScript. Requires HTML5.',
            'permissions' => 'No special permissions required',
            'isAccessibleForFree' => true,
            'offers' => [
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'USD'
            ]
        ];

        // Add software version if available
        if ($version = $this->safeSetting('app_version', '1.0')) {
            $schema['softwareVersion'] = $version;
        }

        // Add author/developer
        $schema['author'] = [
            '@type' => 'Organization',
            'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
            'url' => url('/')
        ];

        // Add features
        $features = [];
        if ($this->safeSetting('app_feature_realtime', true)) {
            $features[] = 'Real-time email reception';
        }
        if ($this->safeSetting('app_feature_multiple_domains', true)) {
            $features[] = 'Multiple domain support';
        }
        if ($this->safeSetting('app_feature_auto_delete', true)) {
            $features[] = 'Automatic email deletion';
        }
        if ($this->safeSetting('app_feature_mobile_friendly', true)) {
            $features[] = 'Mobile-friendly interface';
        }

        if (!empty($features)) {
            $schema['featureList'] = $features;
        }

        return $schema;
    }

    /**
     * Generate FAQ schema from FAQ model data
     */
    private function getFAQSchema($faqs = null)
    {
        try {
            // If no FAQs provided, try to get from database
            if (!$faqs && class_exists('App\Models\Faq')) {
                $faqs = \App\Models\Faq::where('status', 1)
                    ->where('lang', function_exists('getCurrentLang') ? getCurrentLang() : 'en')
                    ->limit(10) // Limit to prevent too large schema
                    ->get();
            }

            if (empty($faqs) || !is_iterable($faqs)) {
                return [];
            }

            $questions = [];
            foreach ($faqs as $faq) {
                $questions[] = [
                    '@type' => 'Question',
                    'name' => $faq->title ?? $faq->question ?? '',
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => strip_tags($faq->content ?? $faq->answer ?? '')
                    ]
                ];
            }

            if (empty($questions)) {
                return [];
            }

            return [[
                '@context' => 'https://schema.org',
                '@type' => 'FAQPage',
                'mainEntity' => $questions
            ]];

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('FAQ Schema Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Generate LocalBusiness schema for physical business presence
     */
    private function getLocalBusinessSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
            'description' => $this->safeSetting('organization_description', 'Secure temporary email service provider'),
            'url' => url('/'),
            'telephone' => $this->safeSetting('organization_telephone', ''),
            'email' => $this->safeSetting('organization_email', ''),
            'priceRange' => 'Free',
            'currenciesAccepted' => 'USD',
            'paymentAccepted' => 'Free Service'
        ];

        // Add address if available
        $address = [];
        if ($street = $this->safeSetting('organization_address_street')) {
            $address['streetAddress'] = $street;
        }
        if ($city = $this->safeSetting('organization_address_city')) {
            $address['addressLocality'] = $city;
        }
        if ($region = $this->safeSetting('organization_address_region')) {
            $address['addressRegion'] = $region;
        }
        if ($postal = $this->safeSetting('organization_address_postal')) {
            $address['postalCode'] = $postal;
        }
        if ($country = $this->safeSetting('organization_address_country')) {
            $address['addressCountry'] = $country;
        }

        if (!empty($address)) {
            $address['@type'] = 'PostalAddress';
            $schema['address'] = $address;
        }

        return $schema;
    }

    /**
     * Generate Product schema for the temporary email service
     */
    private function getProductSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Product',
            'name' => $this->safeSetting('service_name', $this->safeSetting('site_name', 'Temporary Email Service')),
            'description' => $this->safeSetting('service_description', 'Free temporary email service for privacy protection and spam prevention'),
            'url' => url('/'),
            'category' => 'Internet Service',
            'brand' => [
                '@type' => 'Brand',
                'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website'))
            ],
            'offers' => [
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'USD',
                'availability' => 'https://schema.org/InStock',
                'seller' => [
                    '@type' => 'Organization',
                    'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
                    'url' => url('/')
                ]
            ]
        ];

        // Add features as product features
        $features = [];
        if ($this->safeSetting('service_feature_privacy', true)) {
            $features[] = 'Privacy Protection';
        }
        if ($this->safeSetting('service_feature_temporary', true)) {
            $features[] = 'Temporary Email Addresses';
        }
        if ($this->safeSetting('service_feature_anonymous', true)) {
            $features[] = 'Anonymous Email';
        }
        if ($this->safeSetting('service_feature_spam_protection', true)) {
            $features[] = 'Spam Protection';
        }

        if (!empty($features)) {
            $schema['additionalProperty'] = array_map(function($feature) {
                return [
                    '@type' => 'PropertyValue',
                    'name' => 'Feature',
                    'value' => $feature
                ];
            }, $features);
        }

        return $schema;
    }

    /**
     * Generate HowTo schema for common email privacy guides
     */
    private function getHowToSchema()
    {
        $howtos = [];

        // How to use temporary email
        $howtos[] = [
            '@context' => 'https://schema.org',
            '@type' => 'HowTo',
            'name' => 'How to Use Temporary Email for Privacy Protection',
            'description' => 'Learn how to protect your privacy using temporary email addresses',
            'totalTime' => 'PT2M',
            'estimatedCost' => [
                '@type' => 'MonetaryAmount',
                'currency' => 'USD',
                'value' => '0'
            ],
            'supply' => [
                [
                    '@type' => 'HowToSupply',
                    'name' => 'Internet Connection'
                ],
                [
                    '@type' => 'HowToSupply',
                    'name' => 'Web Browser'
                ]
            ],
            'step' => [
                [
                    '@type' => 'HowToStep',
                    'name' => 'Visit the temporary email service',
                    'text' => 'Go to the temporary email website and get your disposable email address',
                    'url' => url('/')
                ],
                [
                    '@type' => 'HowToStep',
                    'name' => 'Copy the email address',
                    'text' => 'Copy the generated temporary email address to your clipboard'
                ],
                [
                    '@type' => 'HowToStep',
                    'name' => 'Use for registration',
                    'text' => 'Use the temporary email address when signing up for websites or services'
                ],
                [
                    '@type' => 'HowToStep',
                    'name' => 'Check for emails',
                    'text' => 'Return to the temporary email service to check for received emails'
                ]
            ]
        ];

        return $howtos;
    }
}
